@page "/tutors"
@using ShiningCMusicCommon.Models
@using ShiningCMusicCommon.Enums
@using ShiningCMusicApp.Services
@using ShiningCMusicApp.Services.Interfaces
@using ShiningCMusicApp.Components
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.DropDowns
@inject ITutorApiService TutorApi
@inject ISubjectApiService SubjectApi
@inject ILessonApiService LessonApi
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@inject IDialogService DialogService
@attribute [Authorize(Roles = nameof(UserRoleEnum.Administrator))]

<PageTitle>Tutor Management</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-3 mb-md-4 fs-3 fs-md-1">🎓 <span class="d-none d-sm-inline">Tutor Management</span><span class="d-sm-none">Tutors</span></h1>
            
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading tutors...</p>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <h5 class="mb-2 mb-md-0">Tutors</h5>
                            <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
                                <button class="btn btn-primary" style="min-width: 150px;" @onclick="OpenCreateModal">
                                    <i class="bi bi-plus-circle" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-2">Add New Tutor</span>
                                    <span class="d-sm-none ms-2">Add Tutor</span>
                                </button>
                                <button class="btn btn-secondary" style="min-width: 150px;" @onclick="RefreshData">
                                    <i class="bi bi-arrow-clockwise" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-2">Refresh</span>
                                    <span class="d-sm-none ms-2">Refresh</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-1 p-md-3">
                        <SfGrid DataSource="@tutors" AllowPaging="true" AllowSorting="true" AllowFiltering="true"
                                AllowResizing="true" Height="600" CssClass="mobile-grid">
                            <GridPageSettings PageSize="10"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.FilterBar"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(Tutor.TutorId) HeaderText="ID" Width="50" IsPrimaryKey="true" 
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn Field=@nameof(Tutor.TutorName) HeaderText="Name" Width="150"></GridColumn>
                                <GridColumn Field=@nameof(Tutor.Email) HeaderText="Email" Width="200"></GridColumn>
                                <GridColumn Field=@nameof(Tutor.SubjectId) HeaderText="Subject" Width="100">
                                    <Template>
                                        @{
                                            var tutor = (context as Tutor);
                                            var subject = subjects.FirstOrDefault(s => s.SubjectId == tutor?.SubjectId);
                                        }
                                        <span>@(subject?.SubjectName ?? "No Subject")</span>
                                    </Template>
                                </GridColumn>                                
                                <GridColumn Field=@nameof(Tutor.LoginName) HeaderText="Login Name" Width="100"></GridColumn>
                                <GridColumn Field=@nameof(Tutor.Color) HeaderText="Color" Width="120">
                                    <Template>
                                        @{
                                            var tutor = (context as Tutor);
                                        }
                                        <div class="d-flex align-items-center">
                                            <div style="width: 20px; height: 20px; background-color: @tutor?.Color; border: 1px solid #ccc; border-radius: 50%; margin-right: 8px;"></div>
                                            <span>@tutor?.Color</span>
                                        </div>
                                    </Template>
                                </GridColumn>
                                <GridColumn Field=@nameof(Tutor.CreatedUTC) HeaderText="Created" Width="100" Format="d" 
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="200" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var tutor = (context as Tutor);
                                        }
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-outline-primary" style="width: 50%;" @onclick="() => OpenEditModal(tutor)"
                                                    title="Edit">
                                                <i class="bi bi-pencil" style="color: inherit;"></i>
@*                                                 <span class="ms-2">Edit</span>
 *@                                            </button>
                                            <button class="btn btn-outline-danger" style="width: 50%;" @onclick="() => DeleteTutor(tutor)"
                                                    title="Delete">
                                                <i class="bi bi-trash" style="color: inherit;"></i>
@*                                                 <span class="ms-2">Delete</span>
 *@                                            </button>
                                        </div>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Create/Edit Modal -->
<SfDialog @bind-Visible="showModal" Header="@modalTitle" Width="500px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentTutor" OnValidSubmit="@SaveTutor">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                
                <div class="mb-3">
                    <label class="form-label">Tutor Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentTutor.TutorName" Placeholder="Enter tutor name" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentTutor.TutorName)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Email</label>
                    <SfTextBox @bind-Value="currentTutor.Email" Placeholder="Enter email address" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentTutor.Email)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Subject @if (!isEditMode) { <span class="text-danger">*</span> }</label>
                    @if (isEditMode)
                    {
                        <SfTextBox @bind-Value="currentTutorSubjectName" Readonly="true"
                                   CssClass="form-control" Placeholder="No subject assigned"></SfTextBox>
                        <small class="form-text text-muted">Subject cannot be changed when editing. Create a new tutor to assign a different subject.</small>
                    }
                    else
                    {
                        <SfDropDownList TValue="int?" TItem="Subject" @bind-Value="currentTutor.SubjectId"
                                        DataSource="@subjects" CssClass="form-control" Placeholder="Select a subject">
                            <DropDownListFieldSettings Value="SubjectId" Text="SubjectName"></DropDownListFieldSettings>
                            <DropDownListTemplates TItem="Subject">
                                <ItemTemplate Context="subjectItem">
                                    <span>@((subjectItem as Subject)?.SubjectName)</span>
                                </ItemTemplate>
                            </DropDownListTemplates>
                        </SfDropDownList>
                        <small class="form-text text-muted">Select a subject for this student (required)</small>
                        @if (showSubjectValidation)
                        {
                            <div class="text-danger">Subject is required.</div>
                        }
                    }
                </div>

                <div class="mb-3">
                    <label class="form-label">Login Name</label>
                    <SfTextBox @bind-Value="currentTutor.LoginName" Placeholder="Assigned via user management"
                               CssClass="form-control" Readonly="true"></SfTextBox>
                    <small class="form-text text-muted">Login name is managed through the Admin page user assignment</small>
                </div>
                <div class="mb-3">
                    <label class="form-label">Color</label>
                    <div class="d-flex align-items-center gap-2">
                        <SfColorPicker Value="@(currentTutor.Color ?? "#6C757D")"
                                       ShowButtons="true"
                                       Mode="ColorPickerMode.Picker"
                                       ModeSwitcher="false"
                                       ValueChange="@((ColorPickerEventArgs args) => OnCurrentTutorColorChanged(args.CurrentValue.Hex))"
                                       CssClass="me-2">
                        </SfColorPicker>
                        <SfTextBox @bind-Value="currentTutor.Color"
                                   Placeholder="#6C757D"
                                   CssClass="form-control"
                                   style="max-width: 120px;">
                        </SfTextBox>
                    </div>
                    <small class="form-text text-muted">Choose a color to identify this tutor in the schedule</small>
                    <ValidationMessage For="@(() => currentTutor.Color)" />
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-blue-custom" type="submit" Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        else
                        {
                            <i class="bi @(isEditMode ? "bi-pencil" : "bi-plus-circle")" style="color: white;"></i><span class="ms-2">@(isEditMode ? "Update" : "Create")</span>
                        }
                    </SfButton>
                    <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseModal">
                        <i class="bi bi-x-circle" style="color: inherit;"></i><span class="ms-2">Cancel</span>
                    </SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Delete Confirmation Dialog -->
<DeleteConfirmationDialog />

<!-- Alert Dialog -->
<AlertDialog />

@code {
    private List<Tutor> tutors = new();
    private List<Subject> subjects = new();
    private bool isLoading = true;
    private bool showModal = false;
    private bool isEditMode = false;
    private bool isSaving = false;
    private string modalTitle = "";
    private Tutor currentTutor = new();
    private string currentTutorSubjectName = "";
    private bool showSubjectValidation = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        try
        {
            // Load tutors and subjects
            var tutorsTask = TutorApi.GetTutorsAsync();
            var subjectsTask = SubjectApi.GetSubjectsAsync();

            await Task.WhenAll(tutorsTask, subjectsTask);

            tutors = await tutorsTask;
            subjects = await subjectsTask;

            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {tutors.Count} tutors, and {subjects.Count} subjects");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading data: {ex.Message}");
            await DialogService.ShowErrorAsync("Error loading data", ex.Message);
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private void OpenCreateModal()
    {
        currentTutor = new Tutor { Color = "#6C757D" };
        showSubjectValidation = false;
        isEditMode = false;
        modalTitle = "Create New Tutor";
        showModal = true;
    }

    private void OpenEditModal(Tutor? tutor)
    {
        if (tutor != null)
        {
            currentTutor = new Tutor
            {
                TutorId = tutor.TutorId,
                TutorName = tutor.TutorName,
                Email = tutor.Email,
                LoginName = tutor.LoginName,
                Color = tutor.Color ?? "#6C757D"
            };

            // Set the subject name for readonly display
            var subject = subjects.FirstOrDefault(s => s.SubjectId == tutor.SubjectId);
            currentTutorSubjectName = subject?.SubjectName ?? "No subject assigned";

            isEditMode = true;
            modalTitle = "Edit Tutor";
            showModal = true;
        }
    }

    private void CloseModal()
    {
        showModal = false;
        currentTutor = new();
        currentTutorSubjectName = "";
        showSubjectValidation = false;
        isSaving = false;
    }

    private async Task SaveTutor()
    {
        showSubjectValidation = false;

        if (string.IsNullOrWhiteSpace(currentTutor.TutorName))
        {
            await DialogService.ShowWarningAsync("Tutor name is required.", "Please enter a valid tutor name before saving.");
            return;
        }
        // Validate subject for new tutor only
        if (!isEditMode)
        {
            bool hasValidationErrors = false;

            if (!currentTutor.SubjectId.HasValue || currentTutor.SubjectId <= 0)
            {
                showSubjectValidation = true;
                hasValidationErrors = true;
            }

            if (hasValidationErrors)
            {
                StateHasChanged();
                return;
            }
        }

        isSaving = true;
        try
        {
            bool success;
            if (isEditMode)
            {
                success = await TutorApi.UpdateTutorAsync(currentTutor.TutorId, currentTutor);
                // if (success)
                // {
                //     await JSRuntime.InvokeVoidAsync("alert", "Tutor updated successfully!");
                // }
            }
            else
            {
                var createdTutor = await TutorApi.CreateTutorAsync(currentTutor);
                success = createdTutor != null;
                // if (success)
                // {
                //     await JSRuntime.InvokeVoidAsync("alert", "Tutor created successfully!");
                // }
            }

            if (success)
            {
                CloseModal();
                await LoadData();
            }
            else
            {
                await DialogService.ShowErrorAsync("Failed to save tutor", "Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving tutor: {ex.Message}");
            await DialogService.ShowErrorAsync("Error saving tutor", ex.Message);
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task DeleteTutor(Tutor? tutor)
    {
        if (tutor == null) return;

        try
        {
            // Check if tutor has future lessons
            var lessons = await LessonApi.GetLessonsAsync();
            var futureLessons = lessons.Where(l => l.TutorId == tutor.TutorId && l.StartTime > DateTime.Now).ToList();

            if (futureLessons.Any())
            {
                var message = $"Cannot delete tutor '{tutor.TutorName}' because they have {futureLessons.Count} upcoming lesson(s).";
                var details = "Please cancel or reschedule their future lessons first.";
                await DialogService.ShowWarningAsync(message, details, "Cannot Delete Tutor");
                return;
            }

            var message = $"Are you sure you want to delete tutor '{tutor.TutorName}'?";
            var details = "This action cannot be undone.";

            var confirmed = await DialogService.ShowDeleteConfirmationAsync(
                message,
                details,
                "Delete Tutor");

            if (confirmed)
            {
                var success = await TutorApi.DeleteTutorAsync(tutor.TutorId);
                if (success)
                {
                    // await JSRuntime.InvokeVoidAsync("alert", "Tutor deleted successfully!");
                    await LoadData();
                }
                else
                {
                    await DialogService.ShowErrorAsync("Failed to delete tutor", "Please try again.");
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting tutor: {ex.Message}");
            await DialogService.ShowErrorAsync("Error deleting tutor", ex.Message);
        }
    }

    private void OnCurrentTutorColorChanged(string newColor)
    {
        currentTutor.Color = newColor;
    }
}
