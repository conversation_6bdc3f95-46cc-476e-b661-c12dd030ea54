@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Buttons
@using ShiningCMusicApp.Services.Interfaces
@inject IDialogService DialogService
@implements IDisposable

<SfDialog @bind-Visible="IsVisible" Header="@Title" Width="400px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="false" CssClass="alert-dialog">
    <DialogEvents Closed="OnDialogClosed"></DialogEvents>
    <DialogTemplates>
        <Content>
            <div class="alert-content">
                <div class="d-flex align-items-start mb-3">
                    <i class="@GetIconClass() me-3" style="font-size: 2rem;"></i>
                    <div>
                        <p class="mb-2 fw-semibold">@Message</p>
                        @if (!string.IsNullOrEmpty(Details))
                        {
                            <p class="text-muted small mb-0">@Details</p>
                        }
                    </div>
                </div>
            </div>
        </Content>
    </DialogTemplates>
    <DialogButtons>
        <DialogButton OnClick="OnOkClick" Content="@OkButtonText" CssClass="@GetButtonClass()" IsPrimary="true"></DialogButton>
    </DialogButtons>
</SfDialog>

@code {
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public EventCallback<bool> IsVisibleChanged { get; set; }
    public string Title { get; set; } = "Alert";
    public string Message { get; set; } = "Alert message";
    public string? Details { get; set; }
    public string OkButtonText { get; set; } = "OK";
    public AlertType Type { get; set; } = AlertType.Info;
    [Parameter] public EventCallback OnResult { get; set; }

    private TaskCompletionSource<bool>? _taskCompletionSource;

    protected override void OnInitialized()
    {
        if (DialogService is ShiningCMusicApp.Services.DialogService service)
        {
            service.RegisterAlertDialog(this);
        }
    }

    public Task ShowAsync()
    {
        _taskCompletionSource = new TaskCompletionSource<bool>();
        IsVisible = true;
        InvokeAsync(StateHasChanged);
        return _taskCompletionSource.Task;
    }

    private async Task OnOkClick()
    {
        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(false);
        await OnResult.InvokeAsync();
        _taskCompletionSource?.SetResult(true);
    }

    private async Task OnDialogClosed()
    {
        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(false);
        await OnResult.InvokeAsync();
        _taskCompletionSource?.SetResult(true);
    }

    private string GetIconClass()
    {
        return Type switch
        {
            AlertType.Success => "bi bi-check-circle-fill text-success",
            AlertType.Warning => "bi bi-exclamation-triangle-fill text-warning",
            AlertType.Error => "bi bi-x-circle-fill text-danger",
            AlertType.Info => "bi bi-info-circle-fill text-info",
            _ => "bi bi-info-circle-fill text-info"
        };
    }

    private string GetButtonClass()
    {
        return Type switch
        {
            AlertType.Success => "btn btn-success",
            AlertType.Warning => "btn btn-warning",
            AlertType.Error => "btn btn-danger",
            AlertType.Info => "btn btn-blue-custom",
            _ => "btn btn-blue-custom"
        };
    }

    public void Dispose()
    {
        if (DialogService is ShiningCMusicApp.Services.DialogService service)
        {
            service.UnregisterAlertDialog(this);
        }
    }
}

public enum AlertType
{
    Info,
    Success,
    Warning,
    Error
}
