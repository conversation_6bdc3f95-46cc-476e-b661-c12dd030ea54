@page "/lessons"
@using ShiningCMusicCommon.Models
@using ShiningCMusicCommon.Enums
@using ShiningCMusicCommon.Extensions
@using ShiningCMusicApp.Services
@using ShiningCMusicApp.Services.Interfaces
@using ShiningCMusicApp.Components
@using Syncfusion.Blazor.Schedule
@using Syncfusion.Blazor.Inputs
@using System.Security.Claims
@inject ILessonApiService LessonApi
@inject ITutorApiService TutorApi
@inject IStudentApiService StudentApi
@inject ISubjectApiService SubjectApi
@inject ILocationApiService LocationApi
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@inject IDialogService DialogService
@attribute [Authorize]
@implements IDisposable

<PageTitle>Lesson Time Table</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <AuthorizeView>
                <Authorized>
                    <h1 class="mb-3 mb-md-4 fs-3 fs-md-1">🎵 <span class="d-none d-sm-inline">@GetPageTitle(context.User)</span><span class="d-sm-none">@GetShortPageTitle(context.User)</span></h1>
                </Authorized>
                <NotAuthorized>
                    <h1 class="mb-3 mb-md-4 fs-3 fs-md-1">🎵 <span class="d-none d-sm-inline">Lesson Time Table</span><span class="d-sm-none">Lessons</span></h1>
                </NotAuthorized>
            </AuthorizeView>

            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading lessons...</p>
                </div>
            }
            else
            {
                <AuthorizeView Roles="@UserRoleEnum.Administrator.ToString()">
                    @if (tutors.Any())
                    {
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-palette"></i> <span class="d-none d-sm-inline">Tutor Colors</span><span class="d-sm-none">Colors</span></h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    @foreach (var tutor in tutors)
                                    {
                                        <div class="col-6 col-md-4 col-lg-3 mb-3">
                                            <div class="d-flex align-items-center">
                                                <SfColorPicker Value="@(tutor.Color ?? "#6C757D")"
                                                ShowButtons="true"
                                                Mode="ColorPickerMode.Picker"
                                                ModeSwitcher="false"
                                                ValueChange="@((ColorPickerEventArgs args) => OnTutorColorChanged(tutor.TutorId, args.CurrentValue.Hex))"
                                                CssClass="me-2">
                                                </SfColorPicker>
                                                <small class="text-muted">@tutor.TutorName</small>
                                            </div>
                                        </div>
                                    }
                                </div>
                                <small class="text-warning">
                                    <i class="bi bi-info-circle"></i>
                                    <span class="d-none d-sm-inline">Select a color and click Apply to update all lessons for that tutor.</span>
                                    <span class="d-sm-none">Select color and click Apply.</span>
                                </small>
                            </div>
                        </div>
                    }
                </AuthorizeView>

                <div class="card">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            @if (scheduleEvents.Any())
                            {
                                <h6 class="mb-2 mb-md-0 text-truncate">
                                    <span class="d-none d-lg-inline">Next lesson: @scheduleEvents.Where(e => e.StartTime > DateTime.Now).FirstOrDefault()?.Subject at @scheduleEvents.Where(e => e.StartTime > DateTime.Now).FirstOrDefault()?.StartTime.ToString("g")</span>
                                    <span class="d-lg-none">Next: @scheduleEvents.Where(e => e.StartTime > DateTime.Now).FirstOrDefault()?.Subject</span>
                                </h6>
                            }
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" style="min-width: 150px;" @onclick="ToggleScheduleView">
                                    <i class="bi @(isMobileListView ? "bi-calendar-fill" : "bi-list-ul")" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-2">@(isMobileListView ? "Calendar" : "List")</span>
                                </button>
                                <button class="btn btn-secondary btn-sm" style="min-width: 150px;" @onclick="RefreshData">
                                    <i class="bi bi-arrow-clockwise" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-1">Refresh</span>
                                </button>
@*                                 <button class="btn btn-outline-secondary btn-sm d-md-none" @onclick="ToggleScheduleView">
                                    <i class="fas fa-@(isMobileListView ? "calendar" : "list")"></i>
                                </button> *@
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-1 p-md-3">
                        <!-- Always render the schedule component, but hide it in mobile list view -->
                        <div style="@(isMobileListView ? "display: none;" : "")">
                            <SfSchedule TValue="ScheduleEvent"
                            @bind-SelectedDate="@selectedDate"
                            Height="@(GetScheduleHeight())"
                            @bind-CurrentView="@currentView"
                            FirstDayOfWeek="1"
                            StartHour="06:00"
                            EndHour="22:30"
                            @ref="scheduleRef"
                            CssClass="mobile-schedule">

                                <ScheduleEvents TValue="ScheduleEvent"
                                OnActionBegin="OnActionBegin"
                                ActionCompleted="OnActionComplete"
                                EventRendered="OnEventRendered"
                                OnCellClick="OnCellClick"
                                OnPopupOpen="OnPopupOpen"></ScheduleEvents>

                                <ScheduleTimeScale Enable="true" Interval="30" SlotCount="1"></ScheduleTimeScale>

                                <ScheduleEventSettings DataSource="@scheduleEvents"
                                TValue="ScheduleEvent"
                                AllowAdding="@CanAddEvents"
                                AllowEditing="@CanEditEvents"
                                AllowDeleting="@CanDeleteEvents"
                                AllowEditFollowingEvents="true">
                                    <Template>
                                        @{
                                            var eventData = context as ScheduleEvent;
                                        }
                                        <div class="template-wrap">
                                            <div class="subject">@(eventData?.StudentName)</div>
                                            @if (!string.IsNullOrEmpty(eventData?.TutorName))
                                            {
                                                <div class="tutor">
                                                    <i class="bi bi-person-fill"></i>@(eventData.TutorName)
                                                </div>
                                            }
                                            @if (!string.IsNullOrEmpty(eventData?.SubjectName))
                                            {
                                                <div class="student">
                                                    <i class="bi bi-book-fill"></i>@(eventData.SubjectName)
                                                </div>
                                            }
                                        </div>
                                    </Template>
                                </ScheduleEventSettings>

                                <ScheduleTemplates>
                                    <EditorTemplate Context="eventData">
                                        @{
                                            var scheduleEvent = eventData as ScheduleEvent ?? new ScheduleEvent();

                                            // Only initialize if this is a different event or first time
                                            if (currentEditingEvent == null || currentEditingEvent.Id != scheduleEvent.Id || !recurrenceInitialized)
                                            {
                                                currentEditingEvent = scheduleEvent;
                                                InitializeRecurrenceVariables(scheduleEvent);
                                                recurrenceInitialized = true;
                                            }
                                            else
                                            {
                                                currentEditingEvent = scheduleEvent;
                                            }
                                        }
                                        <div class="custom-event-editor">
                                            <div class="row">
                                                <div class="col-md-12 mb-3">
                                                    <label class="form-label">Subject <span class="text-danger">*</span></label>
                                                    <select class="form-select" @bind="scheduleEvent.SubjectId" @bind:after="OnSubjectSelectionChanged">
                                                        <option value="0">Select Subject First</option>
                                                        @foreach (var subject in subjects)
                                                        {
                                                            <option value="@subject.SubjectId">@subject.SubjectName</option>
                                                        }
                                                    </select>
                                                    <small class="text-muted">Select a subject to filter available students and tutors</small>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">Student <span class="text-danger">*</span></label>
                                                    <select class="form-select" @bind="scheduleEvent.StudentId" @bind:after="OnStudentChanged" disabled="@(scheduleEvent.SubjectId == 0)">
                                                        <option value="0">@(scheduleEvent.SubjectId == 0 ? "Select Subject First" : "Select Student")</option>
                                                        @foreach (var student in GetFilteredStudents())
                                                        {
                                                            <option value="@student.StudentId">@student.StudentName</option>
                                                        }
                                                    </select>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">Tutor <span class="text-danger">*</span></label>
                                                    <select class="form-select" @bind="scheduleEvent.TutorId" @bind:after="OnTutorChanged" disabled="@(scheduleEvent.SubjectId == 0)">
                                                        <option value="0">@(scheduleEvent.SubjectId == 0 ? "Select Subject First" : "Select Tutor")</option>
                                                        @foreach (var tutor in GetFilteredTutors())
                                                        {
                                                            <option value="@tutor.TutorId">@tutor.TutorName</option>
                                                        }
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">Location</label>
                                                    <select class="form-select" @bind="scheduleEvent.LocationId" @bind:after="OnLocationChanged">
                                                        <option value="0">Select Location</option>
                                                        @foreach (var location in locations)
                                                        {
                                                            <option value="@location.LocationId">@location.LocationName</option>
                                                        }
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">Start Time</label>
                                                    <input type="datetime-local" class="form-control" @bind="scheduleEvent.StartTime" />
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">End Time</label>
                                                    <input type="datetime-local" class="form-control" @bind="scheduleEvent.EndTime" />
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">All Day</label>
                                                    <div class="form-check">
                                                        <input type="checkbox" class="form-check-input" @bind="scheduleEvent.IsAllDay" />
                                                        <label class="form-check-label">All Day Event</label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-12 mb-3">
                                                    <label class="form-label">Description</label>
                                                    <textarea class="form-control" rows="3" @bind="scheduleEvent.Description" placeholder="Enter lesson description"></textarea>
                                                </div>
                                            </div>

                                            <!-- Recurrence Section -->
                                            <div class="row">
                                                <div class="col-md-12 mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox"
                                                               @bind="isRecurringEvent" @bind:after="OnRecurrenceToggled" id="recurringCheck">
                                                        <label class="form-check-label fw-semibold" for="recurringCheck">
                                                            <i class="bi bi-arrow-repeat me-1"></i>Make this a recurring lesson
                                                        </label>
                                                    </div>
                                                    <small class="text-muted">Create multiple lessons automatically based on a pattern</small>
                                                </div>
                                            </div>

                                            @if (isRecurringEvent)
                                            {
                                                <div class="row recurring-options">
                                                    <div class="col-md-12 col-lg-4 mb-3">
                                                        <label class="form-label">Repeat Pattern</label>
                                                        <select class="form-select" @bind="selectedRecurrenceType">
                                                            <option value="daily">Daily</option>
                                                            <option value="weekly">Weekly</option>
                                                            <option value="monthly">Monthly</option>
                                                        </select>
                                                    </div>

                                                    <div class="col-md-6 col-lg-4 mb-3">
                                                        <label class="form-label">Every</label>
                                                        <div class="d-flex align-items-center">
                                                            <input type="number" class="form-control me-2" @bind="recurrenceInterval" @bind:event="oninput" min="1" max="99" style="width: 80px;">
                                                            <span class="text-nowrap">@GetIntervalLabel()</span>
                                                        </div>
                                                    </div>

                                                    <div class="col-md-6 col-lg-4 mb-3">
                                                        <label class="form-label">End After</label>
                                                        <div class="d-flex align-items-center">
                                                            <input type="number" class="form-control me-2" @bind="recurrenceCount" @bind:event="oninput" min="1" max="365" style="width: 80px;">
                                                            <span class="text-nowrap">lessons</span>
                                                        </div>
                                                    </div>
                                                </div>

                                                @if (selectedRecurrenceType == "weekly")
                                                {
                                                    <div class="row">
                                                        <div class="col-md-12 mb-3">
                                                            <label class="form-label">Repeat On</label>
                                                            <div class="d-flex flex-wrap gap-2">
                                                                @foreach (var day in weekDays)
                                                                {
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="checkbox"
                                                                               @bind="day.IsSelected" id="<EMAIL>">
                                                                        <label class="form-check-label" for="<EMAIL>">
                                                                            @day.Name
                                                                        </label>
                                                                    </div>
                                                                }
                                                            </div>
                                                        </div>
                                                    </div>
                                                }

                                                <div class="row">
                                                    <div class="col-md-12 mb-3">
                                                        <div class="alert alert-info">
                                                            <i class="bi bi-info-circle me-2"></i>
                                                            <strong>Preview:</strong> @GetRecurrencePreview()
                                                        </div>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </EditorTemplate>
                                    <EditorFooterTemplate>
                                        <div class="custom-editor-footer">
                                            <AuthorizeView Roles="@UserRoleEnum.Administrator.ToString()">
                                                <Authorized Context="authContext">
                                                    <button type="button" class="btn btn-primary me-2" style="min-width: 90px;" @onclick="OnSaveClick">
                                                        <i class="bi bi-check-circle me-1"></i>Save
                                                    </button>
                                                    <button type="button" class="btn btn-secondary" style="min-width: 90px;" @onclick="OnCancelClick">
                                                        <i class="bi bi-x-circle me-1"></i>Cancel
                                                    </button>
                                                </Authorized>
                                                <NotAuthorized Context="authContext">
                                                    <button type="button" class="btn btn-secondary" style="min-width: 90px;" @onclick="OnCancelClick">
                                                        <i class="bi bi-x-circle me-1"></i>Close
                                                    </button>
                                                    <div class="text-muted mt-2">
                                                        <small>You can only view lesson details. Contact an administrator to make changes.</small>
                                                    </div>
                                                </NotAuthorized>
                                            </AuthorizeView>
                                        </div>
                                    </EditorFooterTemplate>
                                </ScheduleTemplates>

                                <ScheduleViews>
                                    <ScheduleView Option="View.Day"></ScheduleView>
                                    <ScheduleView Option="View.Week"></ScheduleView>
                                    <ScheduleView Option="View.Month"></ScheduleView>
                                </ScheduleViews>

                                <ScheduleQuickInfoTemplates TemplateType="TemplateType.Event">
                                    <ContentTemplate>
                                        @{
                                            var eventData = context as ScheduleEvent;
                                        }
                                        <div class="quick-info">
                                            <div class="event-title">@(eventData?.SubjectName)</div>
                                            <div class="event-details">
                                                <p>
                                                    <i class="bi bi-clock"></i>
                                                    @(eventData?.StartTime.ToString("MMM dd, yyyy h:mm tt")) - @(eventData?.EndTime.ToString("h:mm tt"))
                                                </p>
                                                @*
                                            @if (!string.IsNullOrEmpty(eventData?.SubjectName))
                                            {
                                                <p>
                                                    <i class="bi bi-book"></i>
                                                    <strong>Subject:</strong> @(eventData.SubjectName)
                                                </p>
                                            }
                                            *@
                                                @if (!string.IsNullOrEmpty(eventData?.TutorName))
                                                {
                                                    <p>
                                                        <i class="bi bi-person-fill"></i>
                                                        <strong>Tutor:</strong> @(eventData.TutorName)
                                                    </p>
                                                }

                                                @if (!string.IsNullOrEmpty(eventData?.Location))
                                                {
                                                    <p>
                                                        <i class="bi bi-geo-alt"></i>
                                                        <strong>Location:</strong> @(eventData.Location)
                                                    </p>
                                                }
                                                @if (!string.IsNullOrEmpty(eventData?.Description))
                                                {
                                                    <p>
                                                        <i class="bi bi-card-text"></i>
                                                        <strong>Description:</strong> @(eventData.Description)
                                                    </p>
                                                }
                                            </div>
                                        </div>
                                    </ContentTemplate>
                                </ScheduleQuickInfoTemplates>

                            </SfSchedule>
                        </div>

                        @if (isMobileListView)
                        {
                            <!-- Mobile List View -->
                            <div class="mobile-lesson-list">
                                @if (scheduleEvents.Any())
                                {
                                    var groupedEvents = scheduleEvents
                                        .Where(e => e.StartTime >= DateTime.Today)
                                        .OrderBy(e => e.StartTime)
                                        .GroupBy(e => e.StartTime.Date)
                                        .Take(7); // Show next 7 days

                                    @foreach (var dayGroup in groupedEvents)
                                    {
                                        <div class="day-group mb-3">
                                            <h6 class="day-header bg-light p-2 rounded">
                                                @dayGroup.Key.ToString("dddd, MMM dd")
                                            </h6>
                                            @foreach (var lesson in dayGroup)
                                            {
                                                <div class="lesson-card card mb-2 clickable-card"
                                                @onclick="() => OnMobileLessonClick(lesson)"
                                                style="cursor: pointer;">
                                                    <div class="card-body p-3">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <div class="flex-grow-1">
                                                                <h6 class="mb-1">@lesson.StudentName</h6>
                                                                <small class="text-muted">
                                                                    <i class="bi bi-clock"></i> @lesson.StartTime.ToString("h:mm tt") - @lesson.EndTime.ToString("h:mm tt")
                                                                </small>
                                                                @if (!string.IsNullOrEmpty(lesson.TutorName))
                                                                {
                                                                    <br><small class="text-muted">
                                                                        <i class="bi bi-person"></i> @lesson.TutorName
                                                                    </small>
                                                                }
                                                                @if (!string.IsNullOrEmpty(lesson.SubjectName))
                                                                {
                                                                    <br><small class="text-muted">
                                                                        <i class="bi bi-book"></i> @lesson.SubjectName
                                                                    </small>
                                                                }
                                                                @if (!string.IsNullOrEmpty(lesson.Location))
                                                                {
                                                                    <br><small class="text-muted">
                                                                        <i class="bi bi-geo-alt"></i> @lesson.Location
                                                                    </small>
                                                                }
                                                            </div>
                                                            <div class="d-flex align-items-center">
                                                                <small class="text-muted me-2">
                                                                    <i class="bi bi-info-circle"></i>
                                                                </small>
                                                                <div class="lesson-color" style="width: 10px; height: 60px; background-color: @(GetTutorColor(lesson.TutorId)); border-radius: 2px;"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    }
                                }
                                else
                                {
                                    <div class="text-center py-4">
                                        <i class="bi bi-calendar-x fs-1 text-muted"></i>
                                        <p class="text-muted mt-2">No upcoming lessons</p>
                                    </div>
                                }

                                @if (scheduleEvents.Any())
                                {
                                    <div class="text-center mt-3">
                                        <small class="text-muted">
                                            <i class="bi bi-info-circle"></i> Tap any lesson to view details
                                        </small>
                                    </div>
                                }
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Delete Confirmation Dialog -->
<DeleteConfirmationDialog />

<!-- Alert Dialog -->
<AlertDialog />

<style>
    .recurring-options {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-top: 0.5rem;
    }

    .recurring-options .form-label {
        font-weight: 600;
        color: #495057;
    }

    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .alert-info {
        background-color: #d1ecf1;
        border-color: #bee5eb;
        color: #0c5460;
    }

    .custom-event-editor .form-check-label {
        cursor: pointer;
    }

    .custom-event-editor .form-check-input {
        cursor: pointer;
    }

    .recurring-options .d-flex.flex-wrap.gap-2 .form-check {
        margin-right: 1rem;
        margin-bottom: 0.5rem;
    }

    .recurring-options .form-check-label {
        font-weight: normal;
        margin-left: 0.25rem;
    }

    @@media (max-width: 768px) {
        .recurring-options {
            padding: 0.75rem;
        }

        .recurring-options .d-flex.flex-wrap.gap-2 .form-check {
            margin-right: 0.5rem;
        }
    }
</style>

@code {
    private List<ScheduleEvent> scheduleEvents = new();
    private List<Tutor> tutors = new();
    private List<Student> students = new();
    private List<Subject> subjects = new();
    private List<Location> locations = new();
    private DateTime selectedDate = DateTime.Today;
    private View currentView = View.Week;
    private bool isLoading = true;
    private bool isMobileListView = false;
    private SfSchedule<ScheduleEvent>? scheduleRef;
    private ScheduleEvent? currentEditingEvent;
    // private bool isSaveClick = false;

    // Recurrence-related variables
    private bool isRecurringEvent = false;
    private string selectedRecurrenceType = "weekly";
    private int recurrenceInterval = 1;
    private int recurrenceCount = 10;
    private bool recurrenceInitialized = false;
    private List<WeekDay> weekDays = new()
    {
        new WeekDay { Code = "MO", Name = "Mon", IsSelected = false },
        new WeekDay { Code = "TU", Name = "Tue", IsSelected = false },
        new WeekDay { Code = "WE", Name = "Wed", IsSelected = false },
        new WeekDay { Code = "TH", Name = "Thu", IsSelected = false },
        new WeekDay { Code = "FR", Name = "Fri", IsSelected = false },
        new WeekDay { Code = "SA", Name = "Sat", IsSelected = false },
        new WeekDay { Code = "SU", Name = "Sun", IsSelected = false }
    };

    // Role-based permissions
    private AuthenticationState? authState;
    private bool IsAdmin => authState?.User?.IsInRole(UserRoleEnum.Administrator) == true;
    private bool CanAddEvents => IsAdmin;
    private bool CanEditEvents => IsAdmin;
    private bool CanDeleteEvents => IsAdmin;

    protected override async Task OnInitializedAsync()
    {
        // Get authentication state first
        authState = await AuthStateProvider.GetAuthenticationStateAsync();
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        try
        {
            // Load data from API
            var lessonsTask = LessonApi.GetLessonsAsync();
            var tutorsTask = TutorApi.GetTutorsAsync();
            var studentsTask = StudentApi.GetStudentsAsync();
            var subjectsTask = SubjectApi.GetSubjectsAsync();
            var locationsTask = LocationApi.GetLocationsAsync();

            await Task.WhenAll(lessonsTask, tutorsTask, studentsTask, subjectsTask, locationsTask);

            var allLessons = await lessonsTask;
            tutors = await tutorsTask;
            students = await studentsTask;
            subjects = await subjectsTask;
            locations = await locationsTask;

            // Filter lessons based on user role
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            var currentUser = await AuthStateProvider.GetCurrentUserAsync();

            if (authState.User.IsInRole(UserRoleEnum.Tutor))
            {
                // Find tutor by login name
                var currentTutor = tutors.FirstOrDefault(t => t.LoginName == currentUser?.LoginName);
                if (currentTutor != null)
                {
                    scheduleEvents = allLessons.Where(l => l.TutorId == currentTutor.TutorId).ToList();
                }
                else
                {
                    scheduleEvents = new List<ScheduleEvent>();
                }
            }
            else if (authState.User.IsInRole(UserRoleEnum.Student))
            {
                // Find student by login name
                var currentStudent = students.FirstOrDefault(s => s.LoginName == currentUser?.LoginName);
                if (currentStudent != null)
                {
                    scheduleEvents = allLessons.Where(l => l.StudentId == currentStudent.StudentId).ToList();
                }
                else
                {
                    scheduleEvents = new List<ScheduleEvent>();
                }
            }
            else
            {
                // Administrator sees all lessons
                scheduleEvents = allLessons;
            }

            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {scheduleEvents.Count} lessons, {tutors.Count} tutors, {students.Count} students, {subjects.Count} subjects, {locations.Count} locations");

            // Debug: Log the actual lesson data and ensure proper DateTime format
            foreach (var lesson in scheduleEvents)
            {
                await JSRuntime.InvokeVoidAsync("console.log", $"Lesson: {lesson.Subject}, Start: {lesson.StartTime}, End: {lesson.EndTime}");

                // Ensure dates are in local time and properly formatted
                lesson.StartTime = lesson.StartTime.ToLocalTime();
                lesson.EndTime = lesson.EndTime.ToLocalTime();

                // Ensure dates have DateTimeKind.Local
                lesson.StartTime = DateTime.SpecifyKind(lesson.StartTime, DateTimeKind.Local);
                lesson.EndTime = DateTime.SpecifyKind(lesson.EndTime, DateTimeKind.Local);

                // Assign color based on tutor from database
                if (lesson.TutorId > 0)
                {
                    var tutor = tutors.FirstOrDefault(t => t.TutorId == lesson.TutorId);
                    lesson.CategoryColor = tutor?.Color ?? "#6C757D";
                }
            }

            // Force UI update
            // StateHasChanged();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task AddNewLesson()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Add new lesson functionality will be implemented in the next step!");
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private List<ScheduleEvent> GetScheduleEvents()
    {
        return scheduleEvents ?? new List<ScheduleEvent>();
    }

    // private void OnStudentSelectionChanged(int studentId)
    // {
    //     // Set the student ID first
    //     if (currentEditingEvent != null)
    //     {
    //         currentEditingEvent.StudentId = studentId;

    //         // Auto-populate subject and tutor when student is selected
    //         if (studentId > 0)
    //         {
    //             var selectedStudent = students.FirstOrDefault(s => s.StudentId == studentId);
    //             if (selectedStudent != null)
    //             {
    //                 // Auto-populate subject if student has one assigned
    //                 if (selectedStudent.SubjectId.HasValue && selectedStudent.SubjectId > 0)
    //                 {
    //                     currentEditingEvent.SubjectId = selectedStudent.SubjectId.Value;
    //                     currentEditingEvent.SubjectName = subjects.FirstOrDefault(s => s.SubjectId == selectedStudent.SubjectId)?.SubjectName;
    //                 }

    //                 // Auto-populate tutor if student has one assigned
    //                 if (selectedStudent.TutorID.HasValue && selectedStudent.TutorID > 0)
    //                 {
    //                     currentEditingEvent.TutorId = selectedStudent.TutorID.Value;
    //                     currentEditingEvent.TutorName = tutors.FirstOrDefault(t => t.TutorId == selectedStudent.TutorID)?.TutorName;
    //                 }
    //             }
    //         }
    //     }
    //     StateHasChanged();
    // }

    private void OnStudentChanged()
    {
        // This method will be called when student selection changes
        // Note: This method is called in the context of the editor template
        StateHasChanged();
    }

    private void OnTutorChanged()
    {
        // This method will be called when tutor selection changes
        // Auto-assign color based on selected tutor
        // Note: This method is called in the context of the editor template
        // The actual color assignment will happen in the CRUD event handlers
        StateHasChanged();
    }

    private void OnSubjectSelectionChanged()
    {
        // When subject changes, reset student and tutor selections
        if (currentEditingEvent != null)
        {
            currentEditingEvent.StudentId = 0;
            currentEditingEvent.TutorId = 0;
            currentEditingEvent.StudentName = null;
            currentEditingEvent.TutorName = null;
        }
        StateHasChanged();
    }

    private List<Student> GetFilteredStudents()
    {
        if (currentEditingEvent?.SubjectId > 0)
        {
            var filtered = students.Where(s => s.SubjectId == currentEditingEvent.SubjectId).ToList();
            // Debug: Log the filtering
            JSRuntime.InvokeVoidAsync("console.log", $"Filtering students for SubjectId: {currentEditingEvent.SubjectId}");
            JSRuntime.InvokeVoidAsync("console.log", $"Total students: {students.Count}, Filtered students: {filtered.Count}");

            // If no students found for the subject, show all students as fallback
            if (filtered.Count == 0)
            {
                JSRuntime.InvokeVoidAsync("console.log", "No students found for subject, showing all students as fallback");
                return students.ToList();
            }

            return filtered;
        }
        return new List<Student>();
    }

    private List<Tutor> GetFilteredTutors()
    {
        if (currentEditingEvent?.SubjectId > 0)
        {
            var filtered = tutors.Where(t => t.SubjectId == currentEditingEvent.SubjectId).ToList();
            // Debug: Log the filtering
            JSRuntime.InvokeVoidAsync("console.log", $"Filtering tutors for SubjectId: {currentEditingEvent.SubjectId}");
            JSRuntime.InvokeVoidAsync("console.log", $"Total tutors: {tutors.Count}, Filtered tutors: {filtered.Count}");
            foreach (var tutor in tutors)
            {
                JSRuntime.InvokeVoidAsync("console.log", $"Tutor: {tutor.TutorName}, SubjectId: {tutor.SubjectId}");
            }

            // If no tutors found for the subject, show all tutors as fallback
            if (filtered.Count == 0)
            {
                JSRuntime.InvokeVoidAsync("console.log", "No tutors found for subject, showing all tutors as fallback");
                return tutors.ToList();
            }

            return filtered;
        }
        return new List<Tutor>();
    }

    private void OnLocationChanged()
    {
        // Force UI update when location changes
        StateHasChanged();
    }

    private string GetTutorColor(int tutorId)
    {
        var tutor = tutors.FirstOrDefault(t => t.TutorId == tutorId);
        return tutor?.Color ?? "#6C757D"; // Default gray if not found
    }

    private string GetTutorColorByName(string tutorName)
    {
        var tutor = tutors.FirstOrDefault(t => t.TutorName == tutorName);
        return tutor?.Color ?? "#6C757D";
    }



    private async Task OnTutorColorChanged(int tutorId, string newColor)
    {
        // Update the tutor color in the database
        var success = await TutorApi.UpdateTutorColorAsync(tutorId, newColor);

        if (success)
        {
            // Update the local tutor object
            var tutor = tutors.FirstOrDefault(t => t.TutorId == tutorId);
            if (tutor != null)
            {
                tutor.Color = newColor;
            }

            // Update all existing events for this tutor
            foreach (var scheduleEvent in scheduleEvents.Where(e => e.TutorId == tutorId))
            {
                scheduleEvent.CategoryColor = newColor;
            }

            // Refresh the schedule to show updated colors
            if (scheduleRef != null)
            {
                await scheduleRef.RefreshAsync();
            }

            StateHasChanged();

            await JSRuntime.InvokeVoidAsync("alert", $"Color updated for all {tutor?.TutorName} lessons!");
        }
        else
        {
            await JSRuntime.InvokeVoidAsync("alert", "Failed to update tutor color. Please try again.");
        }
    }

    private void OnEventRendered(EventRenderedArgs<ScheduleEvent> args)
    {
        // Apply the tutor color and tooltip to the event
        if (args.Data != null)
        {
            args.CssClasses = new List<string> { "custom-event" };

            // Create tooltip content
            var tooltipText = $"Tutor: {args.Data.TutorName ?? "Not assigned"}\nStudent: {args.Data.StudentName ?? "Not assigned"}\nTime: {args.Data.StartTime:h:mm tt} - {args.Data.EndTime:h:mm tt}";

            if (!string.IsNullOrEmpty(args.Data.Location))
            {
                tooltipText += $"\nLocation: {args.Data.Location}";
            }

            if (!string.IsNullOrEmpty(args.Data.Description))
            {
                tooltipText += $"\nDescription: {args.Data.Description}";
            }

            // Set attributes including color and tooltip
            args.Attributes = new Dictionary<string, object>
            {
                { "title", tooltipText },
                { "data-bs-toggle", "tooltip" },
                { "data-bs-placement", "top" }
            };

            // Apply tutor color if available
            if (!string.IsNullOrEmpty(args.Data.CategoryColor))
            {
                args.Attributes["style"] = $"background-color: {args.Data.CategoryColor} !important; border-color: {args.Data.CategoryColor} !important;";
            }
        }
    }

    private async Task OnActionBegin(ActionEventArgs<ScheduleEvent> args)
    {
        try
        {
            // Check user permissions before allowing any action
            switch (args.ActionType)
            {
                case ActionType.EventCreate:
                    if (!CanAddEvents)
                    {
                        args.Cancel = true;
                        await DialogService.ShowWarningAsync("You don't have permission to create lessons.", "Please contact an administrator for access.");
                        return;
                    }
                    break;
                case ActionType.EventChange:
                    if (!CanEditEvents)
                    {
                        args.Cancel = true;
                        await DialogService.ShowWarningAsync("You don't have permission to edit lessons.", "Please contact an administrator for access.");
                        return;
                    }
                    await HandleRecurringEventEdit(args);
                    break;
                case ActionType.EventRemove:
                    if (!CanDeleteEvents)
                    {
                        args.Cancel = true;
                        await DialogService.ShowWarningAsync("You don't have permission to delete lessons.", "Please contact an administrator for access.");
                        return;
                    }
                    await HandleDeleteEvent(args);
                    break;
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnActionBegin: {ex.Message}");
            args.Cancel = true;
            await DialogService.ShowErrorAsync("Error processing action", ex.Message);
        }
    }

    private async Task OnActionComplete(ActionEventArgs<ScheduleEvent> args)
    {
        await JSRuntime.InvokeVoidAsync("console.log", $"Action: {args.ActionType} completed.");
    }

    private async Task OnPopupOpen(PopupOpenEventArgs<ScheduleEvent> args)
    {
        try
        {
            if (args.Type == PopupType.QuickInfo)
            {
                // Call JavaScript function to fix popup spacing and adaptive behavior
                await JSRuntime.InvokeVoidAsync("fixQuickInfoPopup", IsAdmin);
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnPopupOpen: {ex.Message}");
        }
    }

    // private async Task OnActionComplete(ActionEventArgs<ScheduleEvent> args)
    // {
    //     // Handle custom save logic from EditorFooterTemplate
    //     if (args.ActionType == ActionType.EventCreate && isSaveClick && currentEditingEvent != null)
    //     {
    //         var createdEvent = await LessonApi.CreateLessonAsync(currentEditingEvent);
    //         if (createdEvent != null)
    //         {
    //             currentEditingEvent.Id = createdEvent.Id;
    //             await JSRuntime.InvokeVoidAsync("console.log", $"Created lesson with ID: {createdEvent.Id}");
    //             // await JSRuntime.InvokeVoidAsync("alert", "Lesson created successfully!");
    //             await LoadData();
    //         }
    //         else
    //         {
    //             await JSRuntime.InvokeVoidAsync("alert", "Failed to create lesson. Please try again.");
    //         }
    //     }
    //     else if (args.ActionType == ActionType.EventChange && isSaveClick && currentEditingEvent != null)
    //     {
    //         var success = await LessonApi.UpdateLessonAsync(currentEditingEvent);
    //         if (success)
    //         {
    //             await JSRuntime.InvokeVoidAsync("console.log", $"Updated lesson with ID: {currentEditingEvent.Id}");
    //             // await JSRuntime.InvokeVoidAsync("alert", "Lesson updated successfully!");
    //             await LoadData();
    //         }
    //         else
    //         {
    //             await JSRuntime.InvokeVoidAsync("alert", "Failed to update lesson. Please try again.");
    //         }
    //     }
    //     // else if (args.ActionType == ActionType.EventRemove)
    //     // {
    //     //     // Refresh the data after successful delete operation
    //     //     await LoadData();
    //     //     await JSRuntime.InvokeVoidAsync("console.log", "Data refreshed after delete operation");
    //     // }

    //     // Reset the save flag
    //     isSaveClick = false;
    // }

    private async Task HandleDeleteEvent(ActionEventArgs<ScheduleEvent> args)
    {
        if (args.DeletedRecords?.Any() == true)
        {
            foreach (var deletedEvent in args.DeletedRecords)
            {
                // Call API to delete the lesson
                var success = await LessonApi.DeleteLessonAsync(deletedEvent.Id);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("console.log", $"Deleted lesson with ID: {deletedEvent.Id}");
                    // await JSRuntime.InvokeVoidAsync("alert", "Lesson deleted successfully!");
                    await LoadData();
                }
                else
                {
                    args.Cancel = true;
                    await DialogService.ShowErrorAsync("Failed to delete lesson", "Please try again.");
                    return;
                }
            }
        }
    }

    private string GetPageTitle(ClaimsPrincipal user)
    {
        if (user.IsInRole(UserRoleEnum.Tutor))
        {
            return "My Lessons - Tutor View";
        }
        else if (user.IsInRole(UserRoleEnum.Student))
        {
            return "My Lessons - Student View";
        }
        else if (user.IsInRole(UserRoleEnum.Administrator))
        {
            return "Lesson Time Table - Admin View";
        }
        else
        {
            return "Lesson Time Table";
        }
    }

    private string GetShortPageTitle(ClaimsPrincipal user)
    {
        if (user.IsInRole(UserRoleEnum.Tutor))
        {
            return "My Lessons";
        }
        else if (user.IsInRole(UserRoleEnum.Student))
        {
            return "My Lessons";
        }
        else if (user.IsInRole(UserRoleEnum.Administrator))
        {
            return "Lessons";
        }
        else
        {
            return "Lessons";
        }
    }

    private async Task ToggleScheduleView()
    {
        isMobileListView = !isMobileListView;
        // await LoadData();
    }

    private string GetScheduleHeight()
    {
        return "600px"; // Smaller height for mobile
    }

    private string GetTutorColor(int? tutorId)
    {
        if (tutorId.HasValue)
        {
            var tutor = tutors.FirstOrDefault(t => t.TutorId == tutorId.Value);
            return tutor?.Color ?? "#6C757D";
        }
        return "#6C757D";
    }

    // old method
    // private async Task OnSaveClick()
    // {
    //     if (currentEditingEvent != null)
    //     {
    //         // Validate required fields
    //         if (currentEditingEvent.TutorId == 0 || currentEditingEvent.StudentId == 0)
    //         {
    //             await JSRuntime.InvokeVoidAsync("alert", "Please select both a Tutor and Student for the lesson.");
    //             return;
    //         }

    //         if (currentEditingEvent.SubjectId == 0)
    //         {
    //             await JSRuntime.InvokeVoidAsync("alert", "Please select a subject for the lesson.");
    //             return;
    //         }

    //         if (currentEditingEvent.LocationId == 0)
    //         {
    //             await JSRuntime.InvokeVoidAsync("alert", "Please select a location for the lesson.");
    //             return;
    //         }

    //         // Set names from loaded data
    //         var tutor = tutors.FirstOrDefault(t => t.TutorId == currentEditingEvent.TutorId);
    //         var student = students.FirstOrDefault(s => s.StudentId == currentEditingEvent.StudentId);

    //         if (tutor != null) currentEditingEvent.TutorName = tutor.TutorName;
    //         if (student != null) currentEditingEvent.StudentName = student.StudentName;

    //         // Set color based on tutor
    //         if (string.IsNullOrEmpty(currentEditingEvent.CategoryColor))
    //         {
    //             currentEditingEvent.CategoryColor = GetTutorColor(currentEditingEvent.TutorId);
    //         }

    //         isSaveClick = true;
    //         if (scheduleRef != null)
    //         {
    //             if (currentEditingEvent.Id == 0) // New event
    //             {
    //                 await scheduleRef.AddEventAsync(currentEditingEvent);
    //             }
    //             else // Existing event
    //             {
    //                 await scheduleRef.SaveEventAsync(currentEditingEvent);
    //             }
    //             scheduleRef.CloseEditor();
    //         }
    //     }
    // }

    private async Task OnSaveClick()
    {
        if (currentEditingEvent != null)
        {
            // Validate required fields
            if (currentEditingEvent.TutorId == 0 || currentEditingEvent.StudentId == 0)
            {
                await DialogService.ShowWarningAsync("Please select both a Tutor and Student for the lesson.", "Both fields are required to create a lesson.");
                return;
            }

            if (currentEditingEvent.SubjectId == 0)
            {
                await DialogService.ShowWarningAsync("Please select a subject for the lesson.", "Subject is required to create a lesson.");
                return;
            }

            if (currentEditingEvent.LocationId == 0)
            {
                await DialogService.ShowWarningAsync("Please select a location for the lesson.", "Location is required to create a lesson.");
                return;
            }

            // Validate recurrence settings
            if (!ValidateRecurrenceSettings())
            {
                return;
            }

            // Set recurrence rule
            currentEditingEvent.RecurrenceRule = BuildRecurrenceRule();

            // Ensure dates are saved as utc in database.
            currentEditingEvent.StartTime = currentEditingEvent.StartTime.ToUniversalTime();
            currentEditingEvent.EndTime = currentEditingEvent.EndTime.ToUniversalTime();

            try
            {
                // Determine if this is create or update
                bool isNewEvent = currentEditingEvent.Id == 0;

                if (isNewEvent)
                {
                    var createdEvent = await LessonApi.CreateLessonAsync(currentEditingEvent);
                    if (createdEvent != null)
                    {
                        // await JSRuntime.InvokeVoidAsync("alert", "Lesson created successfully!");
                        await LoadData();
                    }
                    else
                    {
                        await DialogService.ShowErrorAsync("Failed to create lesson", "Please try again.");
                        return;
                    }
                }
                else
                {
                    var success = await LessonApi.UpdateLessonAsync(currentEditingEvent);
                    if (success)
                    {
                        // await JSRuntime.InvokeVoidAsync("alert", "Lesson updated successfully!");
                        await LoadData();
                    }
                    else
                    {
                        await DialogService.ShowErrorAsync("Failed to update lesson", "Please try again.");
                        return;
                    }
                }

                // Reset recurrence state and close the editor after successful save
                ResetRecurrenceState();
                if (scheduleRef != null)
                {
                    scheduleRef.CloseEditor();
                }
            }
            catch (Exception ex)
            {
                await DialogService.ShowErrorAsync("Error saving lesson", ex.Message);
            }
        }
    }

    private void OnCancelClick()
    {
        // Reset recurrence state when canceling
        ResetRecurrenceState();

        // isSaveClick = false;
        if (scheduleRef != null)
        {
            scheduleRef.CloseEditor();
        }
    }

    public async Task OnCellClick(CellClickEventArgs args)
    {
        try
        {
            // Check if user can add events
            if (!CanAddEvents)
            {
                await DialogService.ShowWarningAsync("You don't have permission to create lessons.", "Please contact an administrator for access.");
                return;
            }

            args.Cancel = true; // Cancel default double-click behavior

            // Open editor for creating new event
            if (scheduleRef != null)
            {
                await scheduleRef.OpenEditorAsync(args, CurrentAction.Add);
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnCellClick: {ex.Message}");
        }
    }

    public async Task OnEventClick(EventClickArgs<ScheduleEvent> args)
    {
        try
        {
            // Check if user can edit events
            if (!CanEditEvents)
            {
                // await JSRuntime.InvokeVoidAsync("alert", "You don't have permission to edit lessons.");
                return;
            }

            args.Cancel = true; // Cancel default double-click behavior

            // Determine the action based on whether it's a recurring event
            CurrentAction action = CurrentAction.Save;
            if (!string.IsNullOrEmpty(args.Event.RecurrenceRule))
            {
                action = CurrentAction.EditOccurrence;
            }

            // Open editor for editing existing event
            if (scheduleRef != null)
            {
                await scheduleRef.OpenEditorAsync(args.Event, action);
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnEventClick: {ex.Message}");
        }
    }

    public async Task OnMobileLessonClick(ScheduleEvent lesson)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("console.log", $"Mobile lesson clicked: {lesson.Id} - {lesson.SubjectName}");

            // Try to open quick view
            await OpenLessonQuickView(lesson);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnMobileLessonClick: {ex.Message}");
        }
    }

    private async Task OpenLessonQuickView(ScheduleEvent lesson)
    {
        // Create a custom quick info popup that matches the existing template
        await ShowCustomQuickInfo(lesson);
    }

    // Static methods for JavaScript interop
    [JSInvokable]
    public static async Task EditLessonFromQuickInfo(int lessonId)
    {
        // Find the current page instance and call the edit method
        var currentPage = GetCurrentPageInstance();
        if (currentPage != null)
        {
            await currentPage.EditLessonById(lessonId);
        }
    }

    [JSInvokable]
    public static async Task DeleteLessonFromQuickInfo(int lessonId)
    {
        // Find the current page instance and call the delete method
        var currentPage = GetCurrentPageInstance();
        if (currentPage != null)
        {
            await currentPage.DeleteLessonById(lessonId);
        }
    }

    private static Lessons? GetCurrentPageInstance()
    {
        // This is a simplified approach - in a real application you might use a service
        // For now, we'll use a static reference
        return _currentInstance;
    }

    private static Lessons? _currentInstance;

    protected override void OnInitialized()
    {
        _currentInstance = this;
        base.OnInitialized();
    }

    public void Dispose()
    {
        if (_currentInstance == this)
        {
            _currentInstance = null;
        }
    }

    private async Task EditLessonById(int lessonId)
    {
        try
        {
            // Find the lesson in our current data
            var lesson = scheduleEvents.FirstOrDefault(e => e.Id == lessonId);
            if (lesson != null)
            {
                // Open the editor for this lesson
                if (scheduleRef != null)
                {
                    // Determine the action based on whether it's a recurring event
                    CurrentAction action = CurrentAction.Save;
                    if (!string.IsNullOrEmpty(lesson.RecurrenceRule))
                    {
                        action = CurrentAction.EditOccurrence;
                    }

                    await scheduleRef.OpenEditorAsync(lesson, action);
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error editing lesson: {ex.Message}");
        }
    }

    private async Task DeleteLessonById(int lessonId)
    {
        try
        {
            // Find the lesson in our current data
            var lesson = scheduleEvents.FirstOrDefault(e => e.Id == lessonId);
            if (lesson == null) return;

            // Show custom confirmation dialog
            var message = "Are you sure you want to delete this lesson?";
            var details = $"Student: {lesson.StudentName}\nTutor: {lesson.TutorName}\nTime: {lesson.StartTime:MMM dd, yyyy h:mm tt}";

            var confirmed = await DialogService.ShowDeleteConfirmationAsync(
                message,
                details,
                "Delete Lesson");

            if (confirmed)
            {
                // Delete the lesson using the API
                var success = await LessonApi.DeleteLessonAsync(lesson.Id);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("console.log", $"Deleted lesson with ID: {lesson.Id}");
                    await LoadData(); // Refresh the data
                    StateHasChanged(); // Update the UI
                }
                else
                {
                    await DialogService.ShowErrorAsync("Failed to delete lesson", "Please try again.");
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting lesson: {ex.Message}");
        }
    }

    private async Task ShowCustomQuickInfo(ScheduleEvent lesson)
    {
        // Create HTML content that exactly matches the existing QuickInfo template
        var quickInfoHtml = $@"
            <div class='quick-info' style='font-family: -apple-system, BlinkMacSystemFont, ""Segoe UI"", Roboto, sans-serif; padding: 0;'>
                <div class='event-title' style='font-size: 16px; font-weight: 600; margin-bottom: 12px; color: #333;'>{lesson.SubjectName}</div>
                <div class='event-details'>
                    <p style='margin: 8px 0; display: flex; align-items: center;'>
                        <i class='bi bi-clock' style='margin-right: 8px; color: #666;'></i>
                        {lesson.StartTime:MMM dd, yyyy h:mm tt} - {lesson.EndTime:h:mm tt}
                    </p>";

        if (!string.IsNullOrEmpty(lesson.TutorName))
        {
            quickInfoHtml += $@"
                    <p style='margin: 8px 0; display: flex; align-items: center;'>
                        <i class='bi bi-person-fill' style='margin-right: 8px; color: #666;'></i>
                        <strong>Tutor:</strong>&nbsp;{lesson.TutorName}
                    </p>";
        }

        if (!string.IsNullOrEmpty(lesson.StudentName))
        {
            quickInfoHtml += $@"
                    <p style='margin: 8px 0; display: flex; align-items: center;'>
                        <i class='bi bi-person' style='margin-right: 8px; color: #666;'></i>
                        <strong>Student:</strong>&nbsp;{lesson.StudentName}
                    </p>";
        }

        if (!string.IsNullOrEmpty(lesson.Location))
        {
            quickInfoHtml += $@"
                    <p style='margin: 8px 0; display: flex; align-items: center;'>
                        <i class='bi bi-geo-alt' style='margin-right: 8px; color: #666;'></i>
                        <strong>Location:</strong>&nbsp;{lesson.Location}
                    </p>";
        }

        if (!string.IsNullOrEmpty(lesson.Description))
        {
            quickInfoHtml += $@"
                    <p style='margin: 8px 0; display: flex; align-items: flex-start;'>
                        <i class='bi bi-card-text' style='margin-right: 8px; color: #666; margin-top: 2px;'></i>
                        <strong>Description:</strong>&nbsp;{lesson.Description}
                    </p>";
        }

        // Add edit and delete buttons for admin users
        if (CanEditEvents)
        {
            quickInfoHtml += $@"
                    <div style='margin-top: 16px; padding-top: 16px; border-top: 1px solid #eee; display: flex; gap: 8px; justify-content: flex-end;'>
                        <button onclick='editLesson({lesson.Id})' style='
                            background: transparent;
                            color: #0066cc;
                            border: 1px solid #0066cc;
                            padding: 8px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 16px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: all 0.2s;
                            width: 40px;
                            min-width: 40px;
                            height: 40px;
                        ' onmouseover='this.style.backgroundColor=""#0066cc""; this.style.color=""white""' onmouseout='this.style.backgroundColor=""transparent""; this.style.color=""#0066cc""'>
                            <i class='bi bi-pencil' style='line-height: 1; margin: 0 auto;'></i>
                        </button>
                        <button onclick='deleteLesson({lesson.Id})' style='
                            background: transparent;
                            color: #dc3545;
                            border: 1px solid #dc3545;
                            padding: 8px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 16px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: all 0.2s;
                            width: 40px;
                            min-width: 40px;
                            height: 40px;
                        ' onmouseover='this.style.backgroundColor=""#dc3545""; this.style.color=""white""' onmouseout='this.style.backgroundColor=""transparent""; this.style.color=""#dc3545""'>
                            <i class='bi bi-trash' style='line-height: 1; margin: 0 auto;'></i>
                        </button>
                    </div>";
        }

        quickInfoHtml += @"
                </div>
            </div>";

        // Show the custom quick info popup
        await JSRuntime.InvokeVoidAsync("showCustomQuickInfo", quickInfoHtml, lesson.SubjectName);
    }

    private Task HandleRecurringEventEdit(ActionEventArgs<ScheduleEvent> args)
    {
        // Check if we're editing a recurring event
        var eventToEdit = args.AddedRecords?.FirstOrDefault() ?? args.ChangedRecords?.FirstOrDefault();
        if (eventToEdit != null && !string.IsNullOrEmpty(eventToEdit.RecurrenceRule))
        {
            // Parse the recurrence rule when editing a recurring event
            ParseRecurrenceRule(eventToEdit.RecurrenceRule);
            isRecurringEvent = !string.IsNullOrEmpty(eventToEdit.RecurrenceRule);
        }
        return Task.CompletedTask;
    }

    private void ResetRecurrenceState()
    {
        recurrenceInitialized = false;
        isRecurringEvent = false;
        selectedRecurrenceType = "weekly";
        recurrenceInterval = 1;
        recurrenceCount = 10;

        // Reset week days selection
        foreach (var day in weekDays)
        {
            day.IsSelected = false;
        }
    }

    private void InitializeRecurrenceVariables(ScheduleEvent scheduleEvent)
    {
        try
        {
            // Debug logging
            JSRuntime.InvokeVoidAsync("console.log", $"Initializing recurrence variables. Event ID: {scheduleEvent.Id}, RecurrenceRule: {scheduleEvent.RecurrenceRule}");

            // Reset recurrence variables when editor opens
            if (!string.IsNullOrEmpty(scheduleEvent.RecurrenceRule))
            {
                // Editing existing recurring event
                ParseRecurrenceRule(scheduleEvent.RecurrenceRule);
                isRecurringEvent = true;
                JSRuntime.InvokeVoidAsync("console.log", "Set isRecurringEvent = true (existing recurring event)");
            }
            else
            {
                // Creating new event or editing non-recurring event
                isRecurringEvent = false;
                selectedRecurrenceType = "weekly";
                recurrenceInterval = 1;
                recurrenceCount = 10;

                JSRuntime.InvokeVoidAsync("console.log", $"Initialized defaults: interval={recurrenceInterval}, count={recurrenceCount}");

                // Reset week days selection
                foreach (var day in weekDays)
                {
                    day.IsSelected = false;
                }

                JSRuntime.InvokeVoidAsync("console.log", "Set isRecurringEvent = false (new or non-recurring event)");
            }
        }
        catch (Exception ex)
        {
            JSRuntime.InvokeVoidAsync("console.error", $"Error in InitializeRecurrenceVariables: {ex.Message}");
        }
    }

    // Recurrence-related methods
    private async Task OnRecurrenceToggled()
    {
        try
        {
            // Debug logging
            await JSRuntime.InvokeVoidAsync("console.log", $"Recurrence toggled: {isRecurringEvent}");

            if (currentEditingEvent != null)
            {
                if (isRecurringEvent)
                {
                    // Parse existing recurrence rule if any, or set defaults
                    if (!string.IsNullOrEmpty(currentEditingEvent.RecurrenceRule))
                    {
                        ParseRecurrenceRule(currentEditingEvent.RecurrenceRule);
                    }
                    else
                    {
                        // Set default values for new recurring event
                        selectedRecurrenceType = "weekly";
                        recurrenceInterval = 1;
                        recurrenceCount = 10;

                        await JSRuntime.InvokeVoidAsync("console.log", $"Set default values: interval={recurrenceInterval}, count={recurrenceCount}");

                        // Default to current day of week
                        var currentDay = DateTime.Now.DayOfWeek;
                        foreach (var day in weekDays)
                        {
                            day.IsSelected = false;
                        }

                        // Select current day
                        var todayCode = currentDay switch
                        {
                            DayOfWeek.Monday => "MO",
                            DayOfWeek.Tuesday => "TU",
                            DayOfWeek.Wednesday => "WE",
                            DayOfWeek.Thursday => "TH",
                            DayOfWeek.Friday => "FR",
                            DayOfWeek.Saturday => "SA",
                            DayOfWeek.Sunday => "SU",
                            _ => "MO"
                        };

                        var todayWeekDay = weekDays.FirstOrDefault(d => d.Code == todayCode);
                        if (todayWeekDay != null)
                        {
                            todayWeekDay.IsSelected = true;
                        }
                    }
                }
                else
                {
                    // Clear recurrence rule
                    currentEditingEvent.RecurrenceRule = null;
                }
            }

            // Force UI update
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnRecurrenceToggled: {ex.Message}");
        }
    }

    private string BuildRecurrenceRule()
    {
        if (!isRecurringEvent) return string.Empty;

        var rule = selectedRecurrenceType.ToUpper() switch
        {
            "DAILY" => $"FREQ=DAILY;INTERVAL={recurrenceInterval};COUNT={recurrenceCount}",
            "WEEKLY" => BuildWeeklyRule(),
            "MONTHLY" => $"FREQ=MONTHLY;INTERVAL={recurrenceInterval};COUNT={recurrenceCount}",
            _ => string.Empty
        };

        return rule;
    }

    private string BuildWeeklyRule()
    {
        var selectedDays = weekDays.Where(d => d.IsSelected).Select(d => d.Code).ToList();
        if (!selectedDays.Any())
        {
            // Default to the current day if none selected
            var currentDay = DateTime.Now.DayOfWeek;
            var dayCode = currentDay switch
            {
                DayOfWeek.Monday => "MO",
                DayOfWeek.Tuesday => "TU",
                DayOfWeek.Wednesday => "WE",
                DayOfWeek.Thursday => "TH",
                DayOfWeek.Friday => "FR",
                DayOfWeek.Saturday => "SA",
                DayOfWeek.Sunday => "SU",
                _ => "MO"
            };
            selectedDays.Add(dayCode);
        }

        var byDay = string.Join(",", selectedDays);
        return $"FREQ=WEEKLY;INTERVAL={recurrenceInterval};BYDAY={byDay};COUNT={recurrenceCount}";
    }

    private void ParseRecurrenceRule(string? rule)
    {
        if (string.IsNullOrEmpty(rule))
        {
            isRecurringEvent = false;
            return;
        }

        isRecurringEvent = true;

        // Parse FREQ
        if (rule.Contains("FREQ=DAILY")) selectedRecurrenceType = "daily";
        else if (rule.Contains("FREQ=WEEKLY")) selectedRecurrenceType = "weekly";
        else if (rule.Contains("FREQ=MONTHLY")) selectedRecurrenceType = "monthly";

        // Parse INTERVAL
        var intervalMatch = System.Text.RegularExpressions.Regex.Match(rule, @"INTERVAL=(\d+)");
        if (intervalMatch.Success)
            recurrenceInterval = int.Parse(intervalMatch.Groups[1].Value);

        // Parse COUNT
        var countMatch = System.Text.RegularExpressions.Regex.Match(rule, @"COUNT=(\d+)");
        if (countMatch.Success)
            recurrenceCount = int.Parse(countMatch.Groups[1].Value);

        // Parse BYDAY for weekly
        if (selectedRecurrenceType == "weekly")
        {
            var byDayMatch = System.Text.RegularExpressions.Regex.Match(rule, @"BYDAY=([^;]+)");
            if (byDayMatch.Success)
            {
                var days = byDayMatch.Groups[1].Value.Split(',');
                foreach (var day in weekDays)
                {
                    day.IsSelected = days.Contains(day.Code);
                }
            }
        }
    }

    private string GetIntervalLabel()
    {
        return selectedRecurrenceType switch
        {
            "daily" => recurrenceInterval == 1 ? "day" : "days",
            "weekly" => recurrenceInterval == 1 ? "week" : "weeks",
            "monthly" => recurrenceInterval == 1 ? "month" : "months",
            _ => ""
        };
    }

    private string GetRecurrencePreview()
    {
        if (!isRecurringEvent) return "";

        var pattern = selectedRecurrenceType switch
        {
            "daily" => $"Every {(recurrenceInterval == 1 ? "" : recurrenceInterval + " ")}day{(recurrenceInterval > 1 ? "s" : "")}",
            "weekly" => GetWeeklyPreview(),
            "monthly" => $"Every {(recurrenceInterval == 1 ? "" : recurrenceInterval + " ")}month{(recurrenceInterval > 1 ? "s" : "")}",
            _ => ""
        };

        return $"{pattern} for {recurrenceCount} lesson{(recurrenceCount > 1 ? "s" : "")}";
    }

    private string GetWeeklyPreview()
    {
        var selectedDays = weekDays.Where(d => d.IsSelected).Select(d => d.Name).ToList();
        var daysText = selectedDays.Any() ? string.Join(", ", selectedDays) : "current day";
        var intervalText = recurrenceInterval == 1 ? "week" : $"{recurrenceInterval} weeks";
        return $"Every {intervalText} on {daysText}";
    }

    private bool ValidateRecurrenceSettings()
    {
        if (!isRecurringEvent) return true;

        if (recurrenceInterval < 1 || recurrenceInterval > 99)
        {
            DialogService.ShowWarningAsync("Invalid interval", "Interval must be between 1 and 99");
            return false;
        }

        if (recurrenceCount < 1 || recurrenceCount > 365)
        {
            DialogService.ShowWarningAsync("Invalid count", "Count must be between 1 and 365");
            return false;
        }

        if (selectedRecurrenceType == "weekly" && !weekDays.Any(d => d.IsSelected))
        {
            DialogService.ShowWarningAsync("No days selected", "Please select at least one day for weekly recurrence");
            return false;
        }

        return true;
    }

    // Helper class for week days
    public class WeekDay
    {
        public string Code { get; set; } = "";
        public string Name { get; set; } = "";
        public bool IsSelected { get; set; }
    }
}




