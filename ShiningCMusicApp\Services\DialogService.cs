using Microsoft.AspNetCore.Components;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicApp.Components;

namespace ShiningCMusicApp.Services
{
    public class DialogService : IDialogService
    {
        private DeleteConfirmationDialog? _deleteDialog;
        private readonly List<DeleteConfirmationDialog> _dialogInstances = new();

        public void RegisterDialog(DeleteConfirmationDialog dialog)
        {
            _deleteDialog = dialog;
            _dialogInstances.Add(dialog);
        }

        public void UnregisterDialog(DeleteConfirmationDialog dialog)
        {
            _dialogInstances.Remove(dialog);
            if (_deleteDialog == dialog)
            {
                _deleteDialog = _dialogInstances.LastOrDefault();
            }
        }

        public async Task<bool> ShowDeleteConfirmationAsync(
            string message,
            string? details = null,
            string title = "Confirm Delete",
            string confirmButtonText = "Delete",
            string cancelButtonText = "Cancel")
        {
            if (_deleteDialog == null)
            {
                throw new InvalidOperationException("No delete confirmation dialog is registered. Make sure to include DeleteConfirmationDialog component in your page.");
            }

            // Set the dialog properties
            _deleteDialog.Title = title;
            _deleteDialog.Message = message;
            _deleteDialog.Details = details;
            _deleteDialog.ConfirmButtonText = confirmButtonText;
            _deleteDialog.CancelButtonText = cancelButtonText;

            // Show the dialog and wait for result
            return await _deleteDialog.ShowAsync();
        }
    }
}
