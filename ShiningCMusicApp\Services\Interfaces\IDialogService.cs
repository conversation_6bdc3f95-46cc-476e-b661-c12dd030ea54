using ShiningCMusicApp.Components;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IDialogService
    {
        /// <summary>
        /// Shows a delete confirmation dialog with customizable message and details
        /// </summary>
        /// <param name="message">Main confirmation message</param>
        /// <param name="details">Optional additional details</param>
        /// <param name="title">Dialog title (default: "Confirm Delete")</param>
        /// <param name="confirmButtonText">Confirm button text (default: "Delete")</param>
        /// <param name="cancelButtonText">Cancel button text (default: "Cancel")</param>
        /// <returns>True if user confirmed, false if cancelled</returns>
        Task<bool> ShowDeleteConfirmationAsync(
            string message,
            string? details = null,
            string title = "Confirm Delete",
            string confirmButtonText = "Delete",
            string cancelButtonText = "Cancel");

        /// <summary>
        /// Shows an alert dialog with customizable message and type
        /// </summary>
        /// <param name="message">Main alert message</param>
        /// <param name="details">Optional additional details</param>
        /// <param name="title">Dialog title</param>
        /// <param name="type">Alert type (Info, Success, Warning, Error)</param>
        /// <param name="okButtonText">OK button text (default: "OK")</param>
        Task ShowAlertAsync(
            string message,
            string? details = null,
            string title = "Alert",
            AlertType type = AlertType.Info,
            string okButtonText = "OK");

        /// <summary>
        /// Shows an error alert dialog
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="details">Optional error details</param>
        /// <param name="title">Dialog title (default: "Error")</param>
        Task ShowErrorAsync(string message, string? details = null, string title = "Error");

        /// <summary>
        /// Shows a success alert dialog
        /// </summary>
        /// <param name="message">Success message</param>
        /// <param name="details">Optional success details</param>
        /// <param name="title">Dialog title (default: "Success")</param>
        Task ShowSuccessAsync(string message, string? details = null, string title = "Success");

        /// <summary>
        /// Shows a warning alert dialog
        /// </summary>
        /// <param name="message">Warning message</param>
        /// <param name="details">Optional warning details</param>
        /// <param name="title">Dialog title (default: "Warning")</param>
        Task ShowWarningAsync(string message, string? details = null, string title = "Warning");
    }
}
