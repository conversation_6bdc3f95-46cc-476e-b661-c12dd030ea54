namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IDialogService
    {
        /// <summary>
        /// Shows a delete confirmation dialog with customizable message and details
        /// </summary>
        /// <param name="message">Main confirmation message</param>
        /// <param name="details">Optional additional details</param>
        /// <param name="title">Dialog title (default: "Confirm Delete")</param>
        /// <param name="confirmButtonText">Confirm button text (default: "Delete")</param>
        /// <param name="cancelButtonText">Cancel button text (default: "Cancel")</param>
        /// <returns>True if user confirmed, false if cancelled</returns>
        Task<bool> ShowDeleteConfirmationAsync(
            string message,
            string? details = null,
            string title = "Confirm Delete",
            string confirmButtonText = "Delete",
            string cancelButtonText = "Cancel");
    }
}
